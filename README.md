# YouTube Video Downloader

A fully functional YouTube video downloader web application built with Node.js, Express, and yt-dlp.

## Features

- **Clean Web Interface**: Modern, responsive design with real-time progress tracking
- **Video Information Display**: Shows title, thumbnail, duration, uploader, views, and upload date
- **Multiple Download Options**: Support for video (MP4) and audio-only (MP3) downloads
- **Quality Selection**: Choose between best quality or smallest file size
- **Real-time Progress**: WebSocket-based progress updates during downloads
- **Download Management**: Cancel downloads in progress
- **Cross-platform**: Works on Windows, macOS, and Linux

## Prerequisites

- Node.js 16.0.0 or higher
- npm (comes with Node.js)

## Installation

1. **Clone or download this repository**
   ```bash
   cd YTD
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Install yt-dlp binary** (automatically downloads the latest version)
   ```bash
   npm run install-ytdlp
   ```

## Usage

1. **Start the server**
   ```bash
   npm start
   ```
   
   For development with auto-restart:
   ```bash
   npm run dev
   ```

2. **Open your browser** and navigate to:
   ```
   http://localhost:3000
   ```

3. **Download videos**:
   - Paste a YouTube video URL
   - Click "Get Info" to see video details
   - Choose your preferred format (Video MP4 or Audio MP3)
   - Select quality preference
   - Click "Start Download"
   - Monitor progress in real-time
   - Download the file when complete

## Supported URL Formats

- `https://www.youtube.com/watch?v=VIDEO_ID`
- `https://youtu.be/VIDEO_ID`
- `https://www.youtube.com/embed/VIDEO_ID`

## API Endpoints

### GET /api/health
Check server and yt-dlp status

### POST /api/info
Get video information
```json
{
  "url": "https://www.youtube.com/watch?v=VIDEO_ID"
}
```

### POST /api/download
Start video download
```json
{
  "url": "https://www.youtube.com/watch?v=VIDEO_ID",
  "format": "video|audio",
  "quality": "best|worst"
}
```

### POST /api/cancel/:downloadId
Cancel an active download

## File Structure

```
YTD/
├── bin/                    # yt-dlp binary
├── downloads/              # Downloaded files (auto-created)
├── public/                 # Frontend files
│   ├── index.html         # Main HTML file
│   ├── styles.css         # CSS styling
│   └── script.js          # Frontend JavaScript
├── scripts/               # Utility scripts
│   └── install-ytdlp.js   # yt-dlp installer
├── server.js              # Main server file
├── package.json           # Dependencies and scripts
└── README.md              # This file
```

## Configuration

The server runs on port 3000 by default. You can change this by setting the `PORT` environment variable:

```bash
PORT=8080 npm start
```

## Troubleshooting

### yt-dlp not found
If you get a "yt-dlp not found" error, run:
```bash
npm run install-ytdlp
```

### Download fails
- Ensure the YouTube URL is valid and accessible
- Some videos may be restricted or unavailable in your region
- Private or age-restricted videos may not be downloadable

### Server won't start
- Make sure port 3000 is not in use by another application
- Check that all dependencies are installed with `npm install`

## Legal Notice

This tool is for educational purposes only. Please respect:
- YouTube's Terms of Service
- Copyright laws in your jurisdiction
- Content creators' rights

Only download videos you have permission to download or that are in the public domain.

## License

MIT License - see LICENSE file for details

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## Support

For issues and questions:
1. Check the troubleshooting section above
2. Search existing issues
3. Create a new issue with detailed information
