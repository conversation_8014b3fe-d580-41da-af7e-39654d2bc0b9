const express = require('express');
const cors = require('cors');
const path = require('path');
const fs = require('fs');
const { spawn } = require('child_process');
const WebSocket = require('ws');
const http = require('http');

const app = express();
const server = http.createServer(app);
const wss = new WebSocket.Server({ server });

const PORT = process.env.PORT || 54321;
const DOWNLOADS_DIR = path.join(__dirname, 'downloads');

// Ensure downloads directory exists
if (!fs.existsSync(DOWNLOADS_DIR)) {
    fs.mkdirSync(DOWNLOADS_DIR, { recursive: true });
}

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.static('public'));
app.use('/downloads', express.static(DOWNLOADS_DIR));

// Store active downloads
const activeDownloads = new Map();

// WebSocket connection handling
wss.on('connection', (ws) => {
    console.log('Client connected');
    
    ws.on('close', () => {
        console.log('Client disconnected');
    });
});

// Broadcast to all connected clients
function broadcast(data) {
    wss.clients.forEach((client) => {
        if (client.readyState === WebSocket.OPEN) {
            client.send(JSON.stringify(data));
        }
    });
}

// Get video information
app.post('/api/info', async (req, res) => {
    const { url } = req.body;
    
    if (!url) {
        return res.status(400).json({ error: 'URL is required' });
    }
    
    try {
        const ytdlpPath = getYtDlpPath();
        
        const child = spawn(ytdlpPath, [
            '--dump-json',
            '--no-playlist',
            url
        ]);
        
        let stdout = '';
        let stderr = '';
        
        child.stdout.on('data', (data) => {
            stdout += data.toString();
        });
        
        child.stderr.on('data', (data) => {
            stderr += data.toString();
        });
        
        child.on('close', (code) => {
            if (code === 0) {
                try {
                    const info = JSON.parse(stdout);
                    res.json({
                        title: info.title,
                        duration: info.duration,
                        thumbnail: info.thumbnail,
                        uploader: info.uploader,
                        view_count: info.view_count,
                        upload_date: info.upload_date,
                        formats: info.formats?.map(f => ({
                            format_id: f.format_id,
                            ext: f.ext,
                            quality: f.quality,
                            filesize: f.filesize,
                            format_note: f.format_note
                        })) || []
                    });
                } catch (parseError) {
                    res.status(500).json({ error: 'Failed to parse video information' });
                }
            } else {
                res.status(400).json({ error: stderr || 'Failed to get video information' });
            }
        });
        
    } catch (error) {
        res.status(500).json({ error: error.message });
    }
});

// Download video
app.post('/api/download', async (req, res) => {
    const { url, format = 'best', quality = 'best' } = req.body;
    
    if (!url) {
        return res.status(400).json({ error: 'URL is required' });
    }
    
    const downloadId = Date.now().toString();
    
    try {
        const ytdlpPath = getYtDlpPath();
        const outputTemplate = path.join(DOWNLOADS_DIR, '%(title)s.%(ext)s');
        
        const args = [
            '--format', format === 'audio' ? 'bestaudio/best' : quality,
            '--output', outputTemplate,
            '--newline',
            url
        ];
        
        if (format === 'audio') {
            args.push('--extract-audio', '--audio-format', 'mp3');
        }
        
        const child = spawn(ytdlpPath, args);
        
        activeDownloads.set(downloadId, child);
        
        let filename = '';
        
        child.stdout.on('data', (data) => {
            const output = data.toString();
            console.log('yt-dlp output:', output);
            
            // Parse progress information
            const progressMatch = output.match(/\[download\]\s+(\d+\.?\d*)%/);
            if (progressMatch) {
                const progress = parseFloat(progressMatch[1]);
                broadcast({
                    type: 'progress',
                    downloadId,
                    progress,
                    status: 'downloading'
                });
            }
            
            // Extract filename
            const filenameMatch = output.match(/\[download\] Destination: (.+)/);
            if (filenameMatch) {
                filename = path.basename(filenameMatch[1]);
            }
        });
        
        child.stderr.on('data', (data) => {
            console.error('yt-dlp error:', data.toString());
        });
        
        child.on('close', (code) => {
            activeDownloads.delete(downloadId);
            
            if (code === 0) {
                broadcast({
                    type: 'complete',
                    downloadId,
                    filename,
                    downloadUrl: `/downloads/${filename}`
                });
            } else {
                broadcast({
                    type: 'error',
                    downloadId,
                    error: 'Download failed'
                });
            }
        });
        
        res.json({ downloadId, status: 'started' });
        
    } catch (error) {
        res.status(500).json({ error: error.message });
    }
});

// Cancel download
app.post('/api/cancel/:downloadId', (req, res) => {
    const { downloadId } = req.params;
    const child = activeDownloads.get(downloadId);
    
    if (child) {
        child.kill();
        activeDownloads.delete(downloadId);
        broadcast({
            type: 'cancelled',
            downloadId
        });
        res.json({ status: 'cancelled' });
    } else {
        res.status(404).json({ error: 'Download not found' });
    }
});

// Get yt-dlp path
function getYtDlpPath() {
    const possiblePaths = [
        path.join(__dirname, 'bin', 'yt-dlp.exe'),
        path.join(__dirname, 'bin', 'yt-dlp'),
        'yt-dlp.exe',
        'yt-dlp'
    ];
    
    for (const ytdlpPath of possiblePaths) {
        if (fs.existsSync(ytdlpPath)) {
            return ytdlpPath;
        }
    }
    
    throw new Error('yt-dlp not found. Please run: npm run install-ytdlp');
}

// Health check
app.get('/api/health', (req, res) => {
    try {
        const ytdlpPath = getYtDlpPath();
        res.json({ status: 'ok', ytdlp: 'available' });
    } catch (error) {
        res.json({ status: 'error', error: error.message });
    }
});

server.listen(PORT, () => {
    console.log(`YouTube Video Downloader server running on http://localhost:${PORT}`);
    console.log(`Downloads will be saved to: ${DOWNLOADS_DIR}`);
});
